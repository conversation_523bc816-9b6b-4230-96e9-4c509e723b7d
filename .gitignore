*.iml
.gradle
/local.properties
/.idea/caches
/.idea/libraries
/.idea/modules.xml
/.idea/workspace.xml
/.idea/navEditor.xml
/.idea/assetWizardSettings.xml
.DS_Store
/build
/captures
.externalNativeBuild
.cxx
*.apk
.idea/deploymentTargetDropDown.xml
.idea/misc.xml
app/src/data/output/
app/src/androidTest/assets/clouds/
.idea/androidTestResultsUserPreferences.xml
build-cache/
/app/dev/
/app/nightly/
release-builds-helper.sh
.idea/appInsightsSettings.xml
.idea/shelf
.idea/copilot
.idea/deploymentTargetSelector.xml
.kotlin/
.idea/other.xml

scripts/survival_guide/output/
converted.webp
test_screenshot_log.txt
error.txt
emulator.log
.attach_*
Book.pdf
Book.epub

# Website
_site/
__pycache__/
*.py[cod]
.venv/

# Test data (too large for git)
dem-*.zip
# Mini will eventually be included once it has a release candidate
# !dem-*-mini.zip
